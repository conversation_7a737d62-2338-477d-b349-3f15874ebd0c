<template>
    <div class="contract-change-record">
        <section>
            <sectionTitle title="修改记录"></sectionTitle>
        </section>
        <a-card :bordered="false" :body-style="{ padding: '16px 0' }">
            <a-table
                :columns="columns"
                :data="tableData"
                :pagination="false"
                :scroll="{ x: 1000 }"
                :bordered="{ cell: true }"
            ></a-table>
        </a-card>
    </div>
</template>

<script setup lang="ts">
    import sectionTitle from '@/components/sectionTitle/index.vue';

    const tableData = ref<any[]>([]);

    const columns = computed(() => {
        return [
            {
                title: '修改类型',
                dataIndex: 'changeTime',
                width: 130,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
            {
                title: '修改日期',
                dataIndex: 'changeTime',
                width: 130,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
            {
                title: '修改人',
                dataIndex: 'changeTime',
                width: 130,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
            {
                title: '修改前',
                dataIndex: 'changeTime',
                width: 130,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
            {
                title: '修改后',
                dataIndex: 'changeTime',
                width: 130,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
        ];
    });
</script>

<style scoped lang="less"></style>
