<template>
    <a-drawer
        v-model:visible="visible"
        :title="drawerTitle"
        class="common-drawer"
        :footer="true"
        @cancel="handleCancel"
    >
        <template #footer>
            <a-space>
                <a-button @click="handleCancel">{{ readOnlyMode ? '关闭' : '取消' }}</a-button>
                <template v-if="!readOnlyMode">
                    <a-button @click="handleSave" :loading="submitLoading">暂存</a-button>
                    <a-button type="primary" @click="handleSubmit" :loading="submitLoading">保存&发起审批</a-button>
                </template>
            </a-space>
        </template>

        <div class="temporary-cost-form">
            <a-form
                ref="formRef"
                :model="formData"
                :rules="formRules"
                label-align="right"
                layout="horizontal"
                :label-col-props="{ span: 6 }"
                :wrapper-col-props="{ span: 18 }"
                auto-label-width
            >
                <!-- 基本信息 -->
                <div class="form-section">
                    <SectionTitle title="基本信息" style="margin-bottom: 16px;" />
                    <!-- 第一行：项目、承租人、合同、房间号 -->
                    <a-row :gutter="16">
                        <a-col :span="6">
                            <a-form-item field="projectId" label="项目" required>
                                <ProjectTreeSelect
                                    v-model="formData.projectId"
                                    :min-level="4"
                                    :disabled="readOnlyMode"
                                    @change="handleProjectChange"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="6">
                            <a-form-item field="customerId" label="承租人" required>
                                <a-select
                                    v-model="formData.customerId"
                                    placeholder="请选择承租人"
                                    :disabled="readOnlyMode"
                                    filterable
                                    allow-search
                                    @change="handleCustomerChange"
                                >
                                    <a-option v-for="customer in customerOptions" :key="customer.id" :value="customer.id">
                                        {{ customer.customerName }}
                                    </a-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="6">
                            <a-form-item field="contractId" label="合同" required>
                                <a-select
                                    v-model="formData.contractId"
                                    placeholder="请选择合同"
                                    :disabled="readOnlyMode"
                                    filterable
                                    allow-search
                                    @change="handleContractChange"
                                >
                                    <a-option v-for="contract in contractOptions" :key="contract.id" :value="contract.id">
                                        {{ contract.contractNo }}
                                    </a-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="6">
                            <a-form-item field="roomId" label="房间号">
                                <a-select
                                    v-model="formHelper.roomId"
                                    placeholder="请选择房间号"
                                    :disabled="readOnlyMode"
                                    filterable
                                    @change="handleRoomChange"
                                >
                                    <a-option v-for="room in roomOptions" :key="room.id" :value="room.id">
                                        {{ room.roomName }}
                                    </a-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <!-- 第二行：合同周期、承租单元、账单周期、费用类型 -->
                    <a-row :gutter="16">
                        <a-col :span="6">
                            <a-form-item field="contractPeriod" label="合同周期">
                                <a-input v-model="formHelper.contractPeriod" disabled />
                            </a-form-item>
                        </a-col>
                        <a-col :span="6">
                            <a-form-item field="roomName" label="承租单元">
                                <a-input v-model="formData.roomName" disabled />
                            </a-form-item>
                        </a-col>
                        <a-col :span="6">
                            <a-form-item field="billPeriod" label="账单周期" required>
                                <a-range-picker
                                    v-model="formData.billPeriod"
                                    :disabled="readOnlyMode"
                                    :disabled-date="disabledBillDate"
                                    format="YYYY-MM-DD"
                                    style="width: 100%"
                                    @change="handleBillPeriodChange"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="6">
                            <a-form-item field="subjectId" label="费用类型" required>
                                <a-select
                                    v-model="formData.subjectId"
                                    placeholder="请选择费用类型"
                                    :disabled="readOnlyMode"
                                    @change="handleCostTypeChange"
                                >
                                    <a-option v-for="item in costTypeOptions" :key="item.value" :value="item.value">
                                        {{ item.label }}
                                    </a-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <!-- 第一行：应收金额、应收日期 -->
                    <a-row :gutter="16">
                        <a-col :span="6">
                            <a-form-item field="actualReceivable" label="应收金额" required>
                                <a-input-number
                                    v-model="formData.actualReceivable"
                                    placeholder="请输入应收金额"
                                    :precision="2"
                                    :min="0"
                                    :disabled="readOnlyMode"
                                    style="width: 100%"
                                >
                                    <template #append>元</template>
                                </a-input-number>
                            </a-form-item>
                        </a-col>
                        <a-col :span="6">
                            <a-form-item field="receivableDate" label="应收日期" required>
                                <a-date-picker
                                    v-model="formData.receivableDate"
                                    placeholder="请选择应收日期"
                                    :disabled="readOnlyMode"
                                    style="width: 100%"
                                    format="YYYY-MM-DD"
                                />
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <!-- 第二行：说明 -->
                    <a-row :gutter="16">
                        <a-col :span="24">
                            <a-form-item field="remark" label="说明">
                                <a-textarea
                                    v-model="formData.remark"
                                    placeholder="请输入说明"
                                    :disabled="readOnlyMode"
                                    :max-length="200"
                                    show-word-limit
                                    :auto-size="{ minRows: 2, maxRows: 4 }"
                                />
                            </a-form-item>
                        </a-col>
                    </a-row>
                </div>
                <!-- 附件 -->
                <div class="form-section">
                    <SectionTitle title="附件" style="margin-bottom: 16px;" />
                    <uploadFile
                        v-model="formData.attachments"
                        :readonly="readOnlyMode"
                        :limit="10"
                        accept=".jpg,.jpeg,.png,.pdf,.doc,.docx"
                    />
                </div>

            </a-form>
        </div>


    </a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick } from 'vue'
import { Message } from '@arco-design/web-vue'
import { addTemporaryCost, editTemporaryCost, getContractList, getTemporaryCostDetail, type ContractCostReductionDTO } from '@/api/temporaryCost'
import customerApi, { type CustomerQueryDTO } from '@/api/customer'
import { getCustomerRooms } from '@/api/reduction'
import ProjectTreeSelect from '@/components/projectTreeSelect/index.vue'
import SectionTitle from '@/components/sectionTitle/index.vue'
import uploadFile from '@/components/upload/uploadFile.vue'
import { DictType, getDictLabel } from '@/dict/index'
import { dictData } from '@/dict/data'

// 组件事件
const emit = defineEmits<{
    success: []
    cancel: []
}>()

// 响应式数据
const visible = ref(false)
const submitLoading = ref(false)
const readOnlyMode = ref(false)
const isEdit = ref(false)
const mode = ref<'add' | 'edit' | 'view'>('add')
const formRef = ref()

// 选项数据
const contractOptions = ref<any[]>([])
const customerOptions = ref<any[]>([])
const roomOptions = ref<any[]>([])
const costTypeOptions = ref<any[]>([])

// 计算属性
const drawerTitle = computed(() => {
    const titleMap: Record<string, string> = {
        add: '新增临时收费',
        edit: '编辑临时收费',
        view: '查看临时收费'
    }
    return titleMap[mode.value]
})

const displayActualReceivable = computed(() => {
    return formData.actualReceivable ? formData.actualReceivable.toFixed(2) : '0.00'
})

// 表单数据
const formData = reactive<any>({
    id: undefined,
    projectId: undefined,
    projectName: '',
    customerId: undefined,
    customerName: '',
    contractId: undefined,
    contractNo: '',
    roomName: '',
    subjectId: undefined,
    subjectName: '',
    billPeriod: undefined, // 账单周期（用于表单验证）
    startDate: undefined,
    endDate: undefined,
    receivableDate: new Date().toISOString().split('T')[0], // 默认当天日期
    actualReceivable: undefined,
    remark: '',
    attachments: undefined,
    approveStatus: 0, // 草稿
    approveTime: undefined,
    isDel: false
})

// 表单辅助数据（不提交到接口）
const formHelper = reactive({
    roomId: undefined,
    contractPeriod: '',
    billPeriod: undefined
})

// 表单验证规则
const formRules = {
    projectId: [{ required: true, message: '请选择项目' }],
    customerId: [{ required: true, message: '请选择承租人' }],
    contractId: [{ required: true, message: '请选择合同' }],
    billPeriod: [{ required: true, message: '请选择账单周期' }],
    subjectId: [{ required: true, message: '请选择费用类型' }],
    actualReceivable: [{ required: true, message: '请输入应收金额' }],
    receivableDate: [{ required: true, message: '请选择应收日期' }]
}

// 处理项目变化
const handleProjectChange = (value: string, option?: any) => {
    formData.projectId = value
    if (option && option.name) {
        formData.projectName = option.name
    }

    // 清空相关数据
    formData.customerId = undefined
    formData.customerName = ''
    formData.contractId = undefined
    formData.contractNo = ''
    formData.roomName = ''
    formHelper.roomId = undefined
    formHelper.contractPeriod = ''
    formHelper.billPeriod = undefined

    // 清空选项
    customerOptions.value = []
    contractOptions.value = []
    roomOptions.value = []

    // 根据项目加载客户和合同数据
    if (value) {
        loadProjectData(value)
    }
}

// 处理客户变化
const handleCustomerChange = (value: string) => {
    const customer = customerOptions.value.find(item => item.id === value)
    if (customer) {
        formData.customerName = customer.customerName
        // 清空合同选择，因为要重新根据承租人筛选
        formData.contractId = undefined
        formData.contractNo = ''
        formData.roomName = ''
        formHelper.contractPeriod = ''
        // 清空房间选择，因为房间数据根据承租人来
        formHelper.roomId = undefined
        // 清空账单周期
        formHelper.billPeriod = undefined

        // 根据承租人名称重新获取合同以及房间号
        Promise.all([
            loadContracts({
                customerName: customer.customerName
            }),
            loadRooms(customer.customerName) // 加载该承租人的房间列表
        ])
    }
}

// 处理合同变化
const handleContractChange = (value: string) => {
    const contract = contractOptions.value.find(item => item.id === value)
    if (contract) {
        // 回填承租人名称和承租人id
        formData.customerId = contract.customerId
        formData.customerName = contract.customerName
        formData.contractNo = contract.contractNo
        formData.roomName = contract.roomName
        formHelper.contractPeriod = `${contract.startDate} 至 ${contract.endDate}`

        // 设置合同开始和结束日期
        formData.startDate = contract.startDate
        formData.endDate = contract.endDate

        // 清空账单周期，让用户重新选择
        formHelper.billPeriod = undefined

        loadRooms(formData.customerName) // 加载该承租人的房间列表
    }
}

// 处理房间变化
const handleRoomChange = async (value: string) => {
    const room = roomOptions.value.find(item => item.id === value)
    if (room && formData.projectId) {
        // 保存当前选择的合同ID
        const currentContractId = formData.contractId

        // 根据房间ID筛选合同列表，同时保留承租人姓名筛选条件（并集）
        const queryParams: Record<string, any> = {
            roomId: value
        }

        // 如果已选择承租人，同时传递承租人姓名进行并集筛选
        if (formData.customerName) {
            queryParams.customerName = formData.customerName
        }

        await loadContracts(queryParams)

        // 检查当前选择的合同是否在新的合同列表中
        const contractExists = contractOptions.value.some(contract => contract.id === currentContractId)

        if (!contractExists && currentContractId) {
            // 如果当前选择的合同不在筛选后的合同列表中，清空合同相关信息
            // 但保留承租人信息，因为承租人是独立选择的
            formData.contractId = undefined
            formData.contractNo = ''
            formData.roomName = ''
            formHelper.contractPeriod = ''
            console.log('当前选择的合同不在该房间的合同列表中，已清空合同选择')
        }

        // 清空账单周期
        formHelper.billPeriod = undefined
    }
}

// 账单周期日期禁用规则
const disabledBillDate = (date: Date) => {
    if (!formData.startDate || !formData.endDate) {
        return false
    }

    const contractStart = new Date(formData.startDate)
    const contractEnd = new Date(formData.endDate)

    // 账单周期不能超过合同周期
    return date < contractStart || date > contractEnd
}

// 处理账单周期变化
const handleBillPeriodChange = (value: any) => {
    if (value && value.length === 2) {
        // 同时设置账单周期和开始结束日期
        formData.billPeriod = value
        formData.startDate = value[0]
        formData.endDate = value[1]
        // 设置应收日期默认为账单周期的结束日期
        if (!formData.receivableDate) {
            formData.receivableDate = value[1]
        }
    } else {
        // 清空时同时清空相关字段
        formData.billPeriod = undefined
        formData.startDate = undefined
        formData.endDate = undefined
    }
}

// 处理费用类型变化
const handleCostTypeChange = (value: number) => {
    formData.subjectId = value
    formData.subjectName = getCostTypeLabel(value)
}

// 加载项目相关数据
const loadProjectData = async (projectId: string) => {
    if (!projectId) return

    try {
        await Promise.all([
            loadCustomersByProject(projectId),
            loadContracts({ projectId })
        ])
    } catch (error) {
        console.error('加载项目数据失败:', error)
    }
}

// 加载房间列表
const loadRooms = async (customerName?: string, projectId?: string) => {
    const targetProjectId = projectId || formData.projectId
    if (!targetProjectId) return

    try {
        const response = await getCustomerRooms({
            customerId: '', // 不传customerId
            customerName: customerName || '', // 承租人姓名用于筛选，可选
            projectId: targetProjectId // 项目id固定传递
        })

        if (response && response.data) {
            roomOptions.value = response.data.map((item: any) => ({
                id: item.roomId,
                roomId: item.roomId,
                roomName: item.roomName,
                projectId: item.projectId,
                projectName: item.projectName,
                parcelName: item.parcelName,
                buildingName: item.buildingName,
                floorName: item.floorName,
                propertyType: item.propertyType,
                rentArea: item.rentArea
            }))
        } else {
            roomOptions.value = []
        }
    } catch (error) {
        console.error('加载房间列表失败:', error)
        roomOptions.value = []
    }
}

// 根据项目加载客户列表
const loadCustomersByProject = async (projectId: string) => {
    if (!projectId) return

    try {
        const params: CustomerQueryDTO = {
            pageNum: 1,
            pageSize: 10000,
            projectId: projectId
        }
        const response = await customerApi.getCustomerList(params)

        if (response && response.rows) {
            customerOptions.value = response.rows.map((item: any) => ({
                id: item.id,
                customerId: item.id,
                customerName: item.customerName,
                customerPhone: item.contactPhone,
                customerType: item.customerType
            }))
        } else {
            customerOptions.value = []
        }
    } catch (error) {
        console.error('加载客户列表失败:', error)
        customerOptions.value = []
    }
}

// 加载合同列表
const loadContracts = async (additionalParams: Record<string, any> = {}) => {
    const projectId = additionalParams.projectId || formData.projectId
    if (!projectId) return

    try {
        // 构建查询参数 - 使用临时收费的API参数结构
        const queryParams: ContractCostReductionDTO = {
            projectId: projectId
        }

        // 添加额外的筛选参数
        if (additionalParams.customerName) {
            queryParams.customerName = additionalParams.customerName
        }
        if (additionalParams.contractNo) {
            queryParams.contractNo = additionalParams.contractNo
        }
        if (additionalParams.roomId) {
            queryParams.roomId = additionalParams.roomId
        }

        const response = await getContractList(queryParams)

        if (response && response.data) {
            contractOptions.value = response.data.map((item: any) => ({
                id: item.id,
                contractId: item.id,
                contractNo: item.contractNo,
                customerName: item.customerName,
                customerId: item.customerId,
                roomName: item.roomName,
                startDate: item.startDate,
                endDate: item.endDate
            }))
        } else {
            contractOptions.value = []
        }
    } catch (error) {
        console.error('加载合同列表失败:', error)
        contractOptions.value = []
    }
}



// 获取审批状态文本
const getApproveStatusText = (status: number | undefined) => {
    const statusMap: Record<number, string> = {
        0: '草稿',
        1: '待审批',
        2: '已通过',
        3: '已拒绝'
    }
    return statusMap[status || 0] || '未知'
}

// 获取审批状态颜色
const getApproveStatusColor = (status: number | undefined) => {
    const colorMap: Record<number, string> = {
        0: 'gray',
        1: 'orange',
        2: 'green',
        3: 'red'
    }
    return colorMap[status || 0] || 'gray'
}

// 初始化费用类型选项
const initCostTypeOptions = () => {
    const allCostTypes = dictData[DictType.COST_TYPE_AND_TAX_RATE] || []
    // 排除定金(10)、保证金(20)、租金(30)
    costTypeOptions.value = allCostTypes.filter(item => ![10, 20, 30].includes(item.value))
}

// 获取费用类型标签
const getCostTypeLabel = (subjectId: number | undefined) => {
    if (!subjectId) return ''
    const costTypeItem = dictData[DictType.COST_TYPE_AND_TAX_RATE]?.find(item => item.value === subjectId)
    return costTypeItem?.label || ''
}

// 加载详情数据
const loadDetailData = async (id: string) => {
    try {
        const response = await getTemporaryCostDetail(id)
        if (response && response.data) {
            const detail = response.data

            // 填充表单数据
            Object.assign(formData, {
                id: detail.id,
                projectId: detail.projectId,
                projectName: detail.projectName,
                customerId: detail.customerId,
                customerName: detail.customerName,
                contractId: detail.contractId,
                contractNo: detail.contractNo,
                roomName: detail.roomName,
                subjectId: !!detail.subjectId ? Number(detail.subjectId) : undefined,
                subjectName: detail.subjectName,
                billPeriod: detail.startDate && detail.endDate ? [detail.startDate, detail.endDate] : undefined,
                startDate: detail.startDate,
                endDate: detail.endDate,
                receivableDate: detail.receivableDate,
                actualReceivable: detail.actualReceivable,
                remark: detail.remark,
                attachments: detail.attachments,
                approveStatus: detail.approveStatus,
                approveTime: detail.approveTime,
                isDel: detail.isDel
            })

            // 填充辅助数据
            Object.assign(formHelper, {
                contractPeriod: detail.contractStartDate && detail.contractEndDate
                    ? `${detail.contractStartDate} 至 ${detail.contractEndDate}`
                    : ''
            })

            // 如果有项目ID，加载项目相关数据
            if (detail.projectId) {
                await loadProjectData(detail.projectId)
            }
        }
    } catch (error) {
        console.error('加载详情数据失败:', error)
        Message.error('加载数据失败')
    }
}

// 显示弹框
const show = async (type: 'add' | 'edit' | 'view', data?: any) => {
    visible.value = true
    mode.value = type
    readOnlyMode.value = type === 'view'
    isEdit.value = type === 'edit'

    // 优先初始化费用类型选项
    initCostTypeOptions()

    if (data) {
        if (type === 'add') {
            // 新增模式：重置表单后使用传入的数据
            resetForm()
            Object.assign(formData, data)

            // 如果有项目ID，自动加载项目相关数据
            if (data.projectId) {
                await loadProjectData(data.projectId)
            }
        } else {
            // 编辑和查看模式：调用详情接口获取完整数据
            await loadDetailData(data.id)
        }
    } else {
        // 没有数据时重置表单
        resetForm()
    }
}

const resetForm = () => {
    Object.assign(formData, {
        id: undefined,
        projectId: undefined,
        projectName: '',
        customerId: undefined,
        customerName: '',
        contractId: undefined,
        contractNo: '',
        roomName: '',
        subjectId: undefined,
        subjectName: '',
        billPeriod: undefined,
        startDate: undefined,
        endDate: undefined,
        receivableDate: new Date().toISOString().split('T')[0], // 默认当天日期
        actualReceivable: undefined,
        remark: '',
        attachments: undefined,
        approveStatus: 0,
        approveTime: undefined,
        isDel: false
    })

    Object.assign(formHelper, {
        roomId: undefined,
        contractPeriod: ''
    })
    formRef.value?.clearValidate()
}

const handleCancel = () => {
    visible.value = false
    setTimeout(() => {
        emit('cancel')
    }, 200);
}

const handleSubmit = async () => {
    try {
        const errors = await formRef.value?.validate()
        if (errors) return

        submitLoading.value = true

        // 设置状态为提交审批
        const submitData = {
            ...formData,
            approveStatus: 1,
            attachments: formData.attachments || null
        }
        // 删除不需要提交的字段
        delete submitData.billPeriod

        if (mode.value === 'add') {
            await addTemporaryCost(submitData)
            Message.success('提交成功')
        } else {
            await editTemporaryCost(submitData)
            Message.success('更新成功')
        }

        visible.value = false
        emit('success')
    } catch (error) {
        console.error('提交失败:', error)
    } finally {
        submitLoading.value = false
    }
}

// 暂存方法
const handleSave = async () => {
    try {
        const errors = await formRef.value?.validate()
        if (errors) return

        submitLoading.value = true

        // 设置状态为草稿
        const saveData = {
            ...formData,
            approveStatus: 0,
            attachments: formData.attachments || null
        }
        // 删除不需要提交的字段
        delete saveData.billPeriod

        if (mode.value === 'add') {
            await addTemporaryCost(saveData)
            Message.success('暂存成功')
        } else {
            await editTemporaryCost(saveData)
            Message.success('暂存成功')
        }

        visible.value = false
        emit('success')
    } catch (error) {
        console.error('暂存失败:', error)
    } finally {
        submitLoading.value = false
    }
}

// 暴露方法
defineExpose({
    show
})
</script>

<style scoped lang="less">
.temporary-cost-form {
    .form-section {
        margin-bottom: 24px;

        &:last-child {
            margin-bottom: 0;
        }
    }
}
</style>
