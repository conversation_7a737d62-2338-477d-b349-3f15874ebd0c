<template>
    <div class="contract-flow">
        <section>
            <sectionTitle title="流水信息"></sectionTitle>
            <a-card :bordered="false" :body-style="{ padding: '16px 0' }">
                <a-table
                    :columns="columns"
                    :data="tableData"
                    :pagination="false"
                    :scroll="{ x: 1000 }"
                    :bordered="{ cell: true }"
                >
                </a-table>
            </a-card>
        </section>
    </div>
</template>

<script setup lang="ts">
    import sectionTitle from '@/components/sectionTitle/index.vue';

    const tableData = ref<any[]>([]);

    const columns = computed(() => {
        return [
            {
                title: '账单类型',
                dataIndex: 'flowType',
                width: 100,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
            {
                title: '实际支付时间',
                dataIndex: 'actualPayTime',
                width: 130,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
            {
                title: '支付类型',
                dataIndex: 'billAmount',
                width: 100,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
            {
                title: '支付方式',
                dataIndex: 'billStatus',
                width: 110,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
            {
                title: '支付单号',
                dataIndex: 'billNo',
                width: 100,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
            {
                title: '实际支付金额',
                dataIndex: 'billNo',
                width: 120,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
            {
                title: '本次记账金额',
                dataIndex: 'billNo',
                width: 120,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
            {
                title: '确认状态',
                dataIndex: 'billNo',
                width: 100,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
            {
                title: '确认时间',
                dataIndex: 'billNo',
                width: 130,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
            {
                title: '确认人',
                dataIndex: 'billNo',
                width: 130,
                align: 'center',
                ellipsis: true,
                tooltip: true,
            },
        ];
    });
</script>

<style scoped lang="less"></style>
